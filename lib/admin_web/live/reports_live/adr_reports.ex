defmodule AdminWeb.ReportsLive.AdrReports do
  use AdminWeb, :live_view

  import AdminWeb.GadStyles.IndexComponents
  import AdminWeb.Components.PieChart
  import Ecto.Query

  alias Crm.Repo
  alias Crm.{Dial, Project, EffortProjects, Effort, Demo, GrossNames, History, Crc, Day}
  alias Dialer.ViciDial
  alias Dialer.ViciDialSchemas.{CrmMapping, ViciDialCampaigns}
  alias Dialer.ViciDialSchemas.Day, as: ViciDay

  alias Dialer.Landline.Repo, as: LandlineRepo
  alias Dialer.Wireless.Repo, as: WirelessRepo


  @impl true
  def mount(_params, _session, socket) do
    # Example usage of the updated functions with date ranges
    # You can modify these dates or get them from params
    range_start = ~N[2024-01-01 00:00:00]
    range_end = ~N[2024-12-31 23:59:59]
    effort_id = 1  # Replace with actual effort_id from params or session

    socket =
      socket
      |> assign(:search_value, "")
      |> assign(:results, nil)
      |> assign(container_class: "w-full p-0")
      #|> stream_configure(:adr_reports, dom_id: &"adr-report-#{&1.id}")
      |> assign(:rows, get_report_rows())
      |> assign(:columns, get_report_columns())
      |> assign(:bus_code_data, get_bus_code_data())
      |> assign(:job_title_code_data, get_job_title_code_data())
      |> assign(:pub_format_code_data, get_pub_format_code_data())
      |> assign(:w2r_values, get_w2r_values())
      |> assign(:w2r_options, get_w2r_options())
      # Add the actual query results - uncomment when ready to test
      # |> assign(:stats_data, stats_query(effort_id, range_start, range_end))
      # |> assign(:hours_data, get_hours(range_start, range_end, effort_id))
      # For testing without effort_id filtering:
      # |> assign(:hours_data, get_hours_simple(range_start, range_end))

    {:ok, assign(socket, :page_title, "ADR Reports")}

  end

  # defp apply_action(socket, :effort, %{"id" => id}) do
  #   socket
  #   |> assign(:page_title, "Edit Report")
  #   |> assign(
  #     :report,
  #     Ash.get!(OutboundSMSBatch, id, actor: socket.assigns.current_user)
  #   )
  # end

  def get_report_columns() do
    ["Dialer", "Source", "Gross Names Received", "Callable Names", "Match Rate", "Total Leads Used",
    "Base Orders", "Alt Pub Count","PassAlongs", "Total Orders w/ Alt", "Total Orders w/ Pass",
    "Total Wrong Numbers","Total Do Not Calls", "Max Attempts", "Total NI's", "Sales/Total Leads Used",
    "Sales/Names Received", "List Penetration", "Landline Hours", "Wireless Hours","Total Expense", "CPO"
    ]
  end

  def get_report_rows() do
    [
      {"Landline", %{"Source"=> "GADX0425", "Gross Names Received"=> "0", "Callable Names"=> "100.00", "Match Rate"=> "45%",
              "Total Leads Used"=> 400, "Base Orders"=> 23, "Alt Pub Count"=> 2, "PassAlongs"=> 2, "Total Orders w/ Alt"=> 2,
               "Total Orders w/ Pass"=> 2, "Total Wrong Numbers"=> 2, "Total Do Not Calls"=> 2, "Max Attempts"=> 2,
               "Total NI's"=> 2, "Sales/Total Leads Used"=> 2, "Sales/Names Received"=> 2, "List Penetration"=> 2,
               "Landline Hours"=> 2, "Wireless Hours"=> 2, "Total Expense"=> 2, "CPO"=> 2}

      },
      {"Wireless", %{"Source"=> "GADX0725", "Gross Names Received"=> "0", "Callable Names"=> "100.00", "Match Rate"=> "45%",
              "Total Leads Used"=> 400, "Base Orders"=> 23, "Alt Pub Count"=> 2, "PassAlongs"=> 2, "Total Orders w/ Alt"=> 2,
               "Total Orders w/ Pass"=> 2, "Total Wrong Numbers"=> 2, "Total Do Not Calls"=> 2, "Max Attempts"=> 2,
               "Total NI's"=> 2, "Sales/Total Leads Used"=> 2, "Sales/Names Received"=> 2, "List Penetration"=> 2,
               "Landline Hours"=> 28, "Wireless Hours"=> 76, "Total Expense"=> 2, "CPO"=> 2}
      },
      {"Landline", %{"Source"=> "GADX0425", "Gross Names Received"=> "0", "Callable Names"=> "100.00", "Match Rate"=> "45%",
              "Total Leads Used"=> 400, "Base Orders"=> 23, "Alt Pub Count"=> 2, "PassAlongs"=> 2, "Total Orders w/ Alt"=> 2,
               "Total Orders w/ Pass"=> 2, "Total Wrong Numbers"=> 2, "Total Do Not Calls"=> 2, "Max Attempts"=> 2,
               "Total NI's"=> 2, "Sales/Total Leads Used"=> 2, "Sales/Names Received"=> 2, "List Penetration"=> 2,
               "Landline Hours"=> 2, "Wireless Hours"=> 2, "Total Expense"=> 2, "CPO"=> 2}

      }
    ]
  end

    defp get_bus_code_data() do
    [
      %{name: "01", value: 25000},
      %{name: "02", value: 28000},
      %{name: "04", value: 32000},
      %{name: "07", value: 4000},
      %{name: "12", value: 25000},
      %{name: "14", value: 28000},
      %{name: "28", value: 32000},
      %{name: "40", value: 4000},
      %{name: "98", value: 25000},
      %{name: "99", value: 28000},
      %{name: "05", value: 32000},
      %{name: "08", value: 4000}
    ]
  end

  defp get_job_title_code_data() do
    [
      %{name: "01", value: 25000},
      %{name: "02", value: 28000},
      %{name: "04", value: 32000},
      %{name: "07", value: 4000},
      %{name: "12", value: 25000},
      %{name: "14", value: 28000},
      %{name: "28", value: 32000},
      %{name: "40", value: 4000},
      %{name: "98", value: 25000}
    ]
  end

  defp get_pub_format_code_data() do
    [
      %{name: "P", value: 25000},
      %{name: "D", value: 28000},
      %{name: "B", value: 32000}
    ]
  end

  defp get_w2r_values() do
    [
      %{name: "SA", value: 2500},
      %{name: "Y", value: 2800},
      %{name: "N", value: 320}
    ]
  end

  defp get_w2r_options() do
    [
      "SA",
      "Y",
      "N"
    ]
  end

  def stats_query(effort_id, range_start \\ nil, range_end \\ nil) do
    # Set default date range if not provided
    rr_end = range_end


    query =
      from(d in Dial,
        # joins - using proper schema modules
        left_join: day in Day, on: day.day == fragment("date(?)", d.last_called),
        left_join: h in History, on: h.history_id == d.last_history_id,
        left_join: crc in Crc, on: crc.crc == d.crc,
        join: child in Project, on: child.id == d.project_id,
         left_join: gn in GrossNames,
          on: gn.projectid == child.id and gn.sourceid == d.source_id,
        join: parent in Project, on: parent.id == child.parent_id,
        join: ep in EffortProjects, on: parent.id == ep.project_id,
        join: e in Effort, on: ep.effort_id == e.effort_id,
        #left_join: r in Demo, on: r.contact_id == d.dial_id,

        # optionally the lateral/demo join for Pub2Count / ReferralCount
        # left_join: p2 in subquery(
        #   from r in Demos,
        #     join: c_child in Project, on: c_child.project_id == r.project_id,
        #     join: c_parent in Project, on: c_parent.project_id == c_child.parent_id,
        #     join: ep2 in EffortProjects, on: c_parent.project_id == ep2.project_id,
        #     join: e2 in Efforts, on: ep2.effort_id == e2.effort_id,
        #     where: r.contact_id == ^d.dial_id
        #     and fragment("trim(?)", r.response) != ""
        #     and e2.effort_id == ^effort_id
        #     and ^d.crc in ["VS", "VSP", "VSQ", "VSQP", "VSW"]
        #     and r.date_time >= ^range_start
        #     and r.date_time < ^rr_end,
        #     group_by: r.contact_id,
        #     select: %{
        #       contact_id: r.contact_id,
        #       referral_count: count(
        #         fragment("CASE WHEN ? REGEXP '^Referal[FN]' THEN ? END", r.qname, r.contact_id)
        #       ),
        #       pub2_count: count(
        #         fragment("CASE WHEN ? REGEXP 'WishToRecieve2' AND TRIM(?) NOT IN ['N', '2'] THEN ? END", r.qname, r.response, r.contact_id)
        #         )
        #     }
        # ), on: p2.contact_id == d.dial_id,

        # where clause
        where: e.effort_id == ^effort_id,

        # group by
        group_by: [child.dialer, gn.sourceid, day.day],
        select: %{
          dialer: child.dialer,  # or perhaps fragment or case if you need logic on History.SystemID like in the commented-out part
          source_id: gn.sourceid,
          gross_names: max(gn.grossnames),
          callable: count(d.dial_id),


          landline_campaign_id: max(parent.landline_campaign_id),
          wireless_campaign_id: max(parent.wireless_campaign_id),
          # parent_id: max(parent.id),

          leads_used: sum(
            fragment(
              "CASE WHEN ? BETWEEN ? AND ? AND ? THEN COALESCE(?, 0) ELSE 0 END",
              d.last_called, ^range_start, ^rr_end, true, crc.final_crc
            )
          ),

          base_orders: sum(
            fragment(
              "CASE WHEN ? BETWEEN ? AND ? AND ? IN ('VS','VSP','VSQ','VSQP','VSW') THEN 1 ELSE 0 END",
              d.last_called, ^range_start, ^rr_end, d.crc
            )
          ),

          max_attempts_but_not_final_crc: sum(
            fragment(
              "CASE WHEN ? >= ? AND COALESCE(?, 0) != 1 THEN 1 ELSE 0 END",
              d.attempts, child.max_attempts, crc.final_crc
            )
          ),

          max_attempts: sum(
            fragment(
              "CASE WHEN ? >= ? THEN 1 ELSE 0 END",
              d.attempts, child.max_attempts
            )
          ),

          # pub2_count: coalesce(sum(p2.pub2_count), 0),
          # referral_count: coalesce(sum(p2.referral_count), 0),
          # referral_count: count(
          #       fragment("CASE WHEN ? REGEXP '^Referal[FN]' AND TRIM(?) != '' AND ? IN ('VS','VSP','VSQ','VSQP','VSW') AND ? BETWEEN ? AND ?
          #       THEN ? END", r.question_name, r.response, d.crc, r.date_time, ^range_start, ^rr_end, r.contact_id)
          #     ),
          # pub2_count: count(
          #   fragment("CASE WHEN ? REGEXP 'WishToRecieve2' AND TRIM(?) NOT IN ('N', '2','') AND ? IN ('VS','VSP','VSQ','VSQP','VSW') AND ? BETWEEN ? AND ?
          #   THEN ? END", r.question_name, r.response, d.crc, r.date_time, ^range_start, ^rr_end, r.contact_id)
          #   ),

          wrong_numbers: sum(
            fragment(
              "CASE WHEN ? BETWEEN ? AND ? AND ? IN ('ADC','DW','OI','FAX') THEN 1 ELSE 0 END",
              d.last_called, ^range_start, ^rr_end, d.crc
            )
          ),

          dnc: sum(
            fragment(
              "CASE WHEN ? BETWEEN ? AND ? AND ? IN ('DNC','DNCI','DNC-B','DNCJA','DNCSC') THEN 1 ELSE 0 END",
              d.last_called, ^range_start, ^rr_end, d.crc
            )
          ),

          dncva: sum(
            fragment(
              "CASE WHEN date(?) BETWEEN ? AND ? AND ? IN ('DNCVA') THEN 1 ELSE 0 END",
              d.last_called, ^range_start, ^range_end, d.crc
            )
          ),

          ni: sum(
            fragment(
              "CASE WHEN ? BETWEEN ? AND ? AND ( ? LIKE 'NI%' OR ? IN ('DNQ','NLII','NTFS','NOR') ) THEN 1 ELSE 0 END",
              d.last_called, ^range_start, ^rr_end, d.crc, d.crc
            )
          ),

          total_due: fragment(
            "ROUND(?,2)",
            sum(
              fragment(
                "CASE WHEN ? BETWEEN ? AND ? AND ? IN ('VS','VSP','VSQ','VSQP','VSW') THEN COALESCE(?,0) + COALESCE(?,0) ELSE 0 END",
                d.last_called, ^range_start, ^rr_end, d.crc, h.base_value, h.add_value
              )
            )
          ),

          method: max(h.pricing_method),
          base_rate: max(h.base_value),

          sum_base_due: sum(
            fragment(
              "CASE WHEN LOWER(?) IN ('VS','VSP','VSQ','VSQP','VSW','SA') THEN ? ELSE 0 END",
              d.crc, h.base_value
            )
          ),

          add_due: sum(
            fragment(
              "CASE WHEN LOWER(?) IN ('VS','VSP','VSQ','VSQP','VSW','SA') THEN ? ELSE 0 END",
              d.crc, h.add_value
            )
          ),

          day: coalesce(day.day,"Uncalled")
        })

    # x =
    #   from(d in Dial,
    #   left_join: day in Day, on: day.day == fragment("date(?)", d.last_called),
    #     left_join: h in History, on: h.history_id == d.last_history_id,
    #     left_join: crc in Crc, on: crc.crc == d.crc,
    #     join: child in Project, on: child.id == d.project_id,
    #     join: parent in Project, on: parent.id == child.parent_id,
    #     join: gn in GrossNames, on: gn.sourceid == d.source_id and gn.projectid == child.id,
    #      #gn.projectid == child.id and gn.sourceid == d.source_id,
    #     join: ep in EffortProjects, on: parent.id == ep.project_id,
    #     join: e in Effort, on: ep.effort_id == e.effort_id,
    #     left_join: r in Demo, on: r.contact_id == d.dial_id,
    #     where: e.effort_id == ^effort_id,
    #     select: %{effort_id: e.effort_id, project_id: ep.project_id, source_id: gn.sourceid}
    # )

    result = Crm.Repo.all(query)
    result

    # xx = from(gn in GrossNames,
    # #join: d in Dial, on: d.source_id == gn.sourceid,
    # #join: child in Project, on: child.id == gn.projectid,
    # where: gn.projectid == 283559,
    # select: %{grossnames: gn.grossnames, sourceid: gn.sourceid}
    # )
    # Crm.Repo.all(xx, limit: 5)

  end

  def get_hours(range_start, range_end, effort_id) do
    # Alternative approach if EffortID column doesn't exist in CrmMapping:
    # Join through Project -> EffortProjects to get the effort relationship

    query = from(
      al in "vicidial_agent_log",
      join: l in "vicidial_list", on: al.lead_id == l.lead_id,
      join: ls in "vicidial_lists", on: l.list_id == ls.list_id,
      join: cm in CrmMapping, on: ls.campaign_id == cm.campaign_id,
      join: d in ViciDay, on: d.day == fragment("date(?)", al.event_time),
      where: al.event_time >= ^range_start and al.event_time <= ^range_end and
             cm.effort_id == ^effort_id,
      group_by: [ls.campaign_id, l.source_id, d.day],
      select: %{
        source_id: l.source_id,
        campaign_id: ls.campaign_id,
        day: coalesce(d.day, fragment("'Uncalled'")),
        hours: sum(al.wait_sec + al.pause_sec + al.talk_sec + al.dispo_sec + al.dead_sec) / 3600
      }
    )

    LandlineRepo.all(query)
   end

  @impl true
  def render(assigns) do
    ~H"""

    <.page_header
      name="ADR Reports"
      title="ADR Reports"
      prefix="/reports/adr"
      search?
      search_value={@search_value}
      search_results={@results}
      new?
    />

    <div class="flex grid grid-cols-3 gap-4 border-b border-gray-200 pl-12 pb-2">
      <div class="flex">
        <img src={~p"/images/gad-logo.png"} class="h-30 w-50" alt="GAD Logo" />
        <div class="p-5 align-left">
          <p class="text-base font-normal text-gray-800">Graven Austin & Drake, Inc.</p>
          <p class="font-normal text-gray-800">5445 Mark Dabling Bvld, Suite 100</p>
          <p class="font-normal text-gray-800">Colorado Springs, CO 80918</p>
          <p class="font-normal text-gray-800">(719) 548-9292</p>
        </div>
      </div>
      <div class="p-5">
        <p class="mt-2 max-w-4xl text-sm text-gray-500">Report Run Date: <%= Date.utc_today() %></p>
        <p class="mt-2 max-w-4xl text-sm text-gray-500">Order Date Range: <%= Date.utc_today() %> - <%= Date.utc_today() %></p>
        <p class="mt-2 max-w-4xl text-sm text-gray-500">Goal: <%= Date.utc_today() %></p>
      </div>
      <div class="p-5">
        <p class="mt-2 max-w-4xl text-sm text-gray-500">Effort: <%= Date.utc_today() %></p>
        <p class="mt-2 max-w-4xl text-sm text-gray-500">Company: <%= Date.utc_today() %> - <%= Date.utc_today() %></p>
        <p class="mt-2 max-w-4xl text-sm text-gray-500">Contact: <%= Date.utc_today() %></p>
        <p class="mt-2 max-w-4xl text-sm text-gray-500">Date Range: <%= Date.utc_today() %></p>
      </div>
    </div>
    <div class="px-10">
      <.table id="adr_reports" rows={@rows} >

        <:col :let={{id, adr_report}} :for={col <- @columns} label={col}>
          {if col != "Dialer", do: Map.get(adr_report, col), else: id}
        </:col>
      </.table>
    </div>
    <div class="grid grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Primary Business Chart -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold mb-4">Business Code Breakdown</h3>
        <.pie_chart
          id="bus-code-donut-chart"
          title="Primary Business"
          data={@bus_code_data}
          variant="donut"
          inner_radius="40%"
          radius="60%"
          width={500}
          height={400}
          type="pie"
        />
      </div>
      <!-- Job Title Code Chart -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold mb-4">Job Title Code Breakdown</h3>
        <.pie_chart
          id="job-title-code-donut-chart"
          title="Job Title"
          data={@job_title_code_data}
          variant="donut"
          inner_radius="40%"
          radius="60%"
          width={500}
          height={400}
          type="pie"
        />
      </div>
      <!-- PubFormat Chart -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold mb-4">PubFormat Breakdown</h3>
        <.pie_chart
          id="w2r-bar-chart"
          data={@w2r_values}
          title="Count"
          x_axis_data={@w2r_options}
          x_axis_title="W2R Options"
          variant="bar"
          inner_radius="40%"
          radius="60%"
          width={500}
          height={400}
          show_legend={false}
          type="bar"
        />
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-4"> Emails </h2>
        <p> 324 </p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-xl font-semibold mb-4"> Overall Suppression Counts </h2>
        <p> 324 </p>
      </div>
    </div>


    """
  end
end
